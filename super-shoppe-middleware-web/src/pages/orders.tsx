import React, { useState } from 'react';
import { useCurrentUser } from '../hooks/use-current-user';
import {
  useOrdersQuery,
  useOrderStatusesQuery,
  getOrderStatusColor,
  formatOrderStatus,
  ORDER_STATUS,
  type OrderStatusType,
} from '../data-graphql/orders';

const OrdersPage = () => {
  const { user, isLoading: userLoading } = useCurrentUser();
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch orders with current filters
  const {
    data: ordersData,
    isLoading: ordersLoading,
    error: ordersError,
  } = useOrdersQuery({
    first: 15,
    page: currentPage,
    status: selectedStatus === 'all' ? undefined : selectedStatus,
    orderBy: [{ column: 'CREATED_AT', order: 'DESC' }],
  });

  // Fetch order statuses for filter tabs
  const { data: statusesData } = useOrderStatusesQuery();

  if (userLoading || ordersLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-red-600">No user data available.</p>
        </div>
      </div>
    );
  }

  if (ordersError) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <p className="text-red-600">
            Error loading orders. Please try again.
          </p>
        </div>
      </div>
    );
  }

  const orders = ordersData?.orders?.data || [];
  const paginatorInfo = ordersData?.orders?.paginatorInfo;
  const orderStatuses = statusesData?.orderStatuses || [];

  // Create filter options from available statuses
  const filterOptions = [
    { value: 'all', label: 'All' },
    ...orderStatuses.map((status) => ({
      value: status.slug,
      label: status.name,
    })),
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Mobile Header */}
      <div className="bg-white shadow-sm px-4 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-gray-900">Orders</h1>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white border-b px-4 py-2">
        <div className="flex space-x-1 overflow-x-auto">
          {filterOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => {
                setSelectedStatus(option.value);
                setCurrentPage(1); // Reset to first page when changing filter
              }}
              className={`px-3 py-2 text-sm font-medium rounded-lg whitespace-nowrap ${
                selectedStatus === option.value
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Orders List */}
      <div className="px-4 py-4 space-y-3">
        {orders.length === 0 ? (
          <div className="bg-white rounded-lg p-8 text-center">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <svg
                className="w-8 h-8 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No orders found
            </h3>
            <p className="text-gray-600">
              No orders match the selected filter.
            </p>
          </div>
        ) : (
          orders.map((order) => (
            <div key={order.id} className="bg-white rounded-lg shadow-sm p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <h3 className="font-semibold text-gray-900">
                    {order.display_id || `#${order.id}`}
                  </h3>
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${getOrderStatusColor(
                      order.status
                    )}`}
                  >
                    {formatOrderStatus(order.status)}
                  </span>
                </div>
                <button className="text-blue-600 text-sm font-medium">
                  View
                </button>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Customer:</span>
                  <span className="font-medium">
                    {order.customer_name || order.customer?.name || 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Items:</span>
                  <span className="font-medium">
                    {order.orderProducts?.length || 0} items
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Total:</span>
                  <span className="font-semibold text-green-600">
                    ${order.total.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Date:</span>
                  <span className="font-medium">
                    {new Date(order.created_at).toLocaleDateString()} at{' '}
                    {new Date(order.created_at).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary Stats */}
      <div className="px-4 py-4">
        <div className="bg-white rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3">
            Today's Summary
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {
                  orders.filter(
                    (o) => o.status === 'complete' || o.status === 'completed'
                  ).length
                }
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                $
                {orders
                  .filter(
                    (o) => o.status === 'complete' || o.status === 'completed'
                  )
                  .reduce((sum, o) => sum + o.total, 0)
                  .toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">Revenue</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrdersPage;

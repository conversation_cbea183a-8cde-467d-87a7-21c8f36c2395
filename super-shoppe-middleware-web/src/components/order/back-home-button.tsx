import Button from '@components/ui/button';
import Link from '@components/ui/link';
import cn from 'classnames';
import { useTranslation } from 'next-export-i18n';

export function BackHomeButton({ className }: { className?: string }) {
  const { t } = useTranslation();

  return (
    <div className={cn(className)}>
      <Link href={'/orders'} className="w-full sm:w-auto">
        <Button>{t('button-go-home')}</Button>
      </Link>
    </div>
  );
}
